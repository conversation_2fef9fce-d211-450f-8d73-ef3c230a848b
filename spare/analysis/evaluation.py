"""
Evaluation tools for SAE models and interventions.

This module provides comprehensive evaluation metrics and tools for
assessing SAE quality and intervention effectiveness.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple, Callable
import torch
from torch import Tensor
import numpy as np
from dataclasses import dataclass
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score

from ..core.sae import SAE

logger = logging.getLogger(__name__)


@dataclass
class SAEEvaluationResult:
    """Results from SAE evaluation."""
    
    reconstruction_metrics: Dict[str, float]
    sparsity_metrics: Dict[str, float]
    feature_metrics: Dict[str, float]
    overall_score: float
    metadata: Dict[str, Any]


@dataclass
class InterventionEvaluationResult:
    """Results from intervention evaluation."""
    
    accuracy_metrics: Dict[str, float]
    effectiveness_metrics: Dict[str, float]
    side_effects: Dict[str, float]
    overall_score: float
    metadata: Dict[str, Any]


class SAEEvaluator:
    """
    Comprehensive evaluator for SAE models and interventions.
    
    Provides metrics for reconstruction quality, sparsity, feature
    interpretability, and intervention effectiveness.
    """
    
    def __init__(self, device: str = "cpu"):
        """
        Initialize SAE evaluator.
        
        Args:
            device: Device for computation
        """
        self.device = device
        self.evaluation_history = []
        
    def evaluate_sae_reconstruction(
        self,
        sae: SAE,
        test_activations: Tensor,
        compute_detailed_metrics: bool = True,
    ) -> Dict[str, float]:
        """
        Evaluate SAE reconstruction quality.
        
        Args:
            sae: SAE model to evaluate
            test_activations: Test activations for evaluation
            compute_detailed_metrics: Whether to compute detailed metrics
            
        Returns:
            Dictionary of reconstruction metrics
        """
        logger.info(f"Evaluating SAE reconstruction on {test_activations.shape[0]} samples")
        
        sae.eval()
        with torch.no_grad():
            # Forward pass through SAE
            output = sae(test_activations)
            
            # Basic reconstruction metrics
            mse = torch.nn.functional.mse_loss(output.sae_out, test_activations)
            mae = torch.nn.functional.l1_loss(output.sae_out, test_activations)
            
            # Fraction of variance unexplained
            fvu = output.fvu.mean()
            
            # Cosine similarity
            cos_sim = torch.nn.functional.cosine_similarity(
                output.sae_out.flatten(),
                test_activations.flatten(),
                dim=0
            )
            
            # Pearson correlation
            def pearson_correlation(x, y):
                x_centered = x - x.mean()
                y_centered = y - y.mean()
                return (x_centered * y_centered).sum() / (x_centered.norm() * y_centered.norm())
            
            pearson_corr = pearson_correlation(
                output.sae_out.flatten(),
                test_activations.flatten()
            )
            
            metrics = {
                'mse': mse.item(),
                'mae': mae.item(),
                'rmse': torch.sqrt(mse).item(),
                'fvu': fvu.item(),
                'cosine_similarity': cos_sim.item(),
                'pearson_correlation': pearson_corr.item(),
                'explained_variance': 1 - fvu.item(),
            }
            
            if compute_detailed_metrics:
                # Per-dimension metrics
                per_dim_mse = ((output.sae_out - test_activations) ** 2).mean(dim=0)
                per_dim_mae = (output.sae_out - test_activations).abs().mean(dim=0)
                
                metrics.update({
                    'per_dim_mse_mean': per_dim_mse.mean().item(),
                    'per_dim_mse_std': per_dim_mse.std().item(),
                    'per_dim_mae_mean': per_dim_mae.mean().item(),
                    'per_dim_mae_std': per_dim_mae.std().item(),
                })
                
                # Reconstruction quality distribution
                sample_mse = ((output.sae_out - test_activations) ** 2).mean(dim=1)
                metrics.update({
                    'sample_mse_mean': sample_mse.mean().item(),
                    'sample_mse_std': sample_mse.std().item(),
                    'sample_mse_median': sample_mse.median().item(),
                })
                
        return metrics
        
    def evaluate_sae_sparsity(
        self,
        sae: SAE,
        test_activations: Tensor,
    ) -> Dict[str, float]:
        """
        Evaluate SAE sparsity characteristics.
        
        Args:
            sae: SAE model to evaluate
            test_activations: Test activations
            
        Returns:
            Dictionary of sparsity metrics
        """
        logger.info("Evaluating SAE sparsity")
        
        sae.eval()
        with torch.no_grad():
            output = sae(test_activations)
            feature_acts = output.feature_acts
            
            # Overall sparsity
            overall_sparsity = output.sparsity.mean().item()
            
            # L0 norm (number of active features per sample)
            l0_norm = (feature_acts > 0).sum(dim=1).float()
            
            # Feature activation frequencies
            feature_frequencies = (feature_acts > 0).float().mean(dim=0)
            
            # Dead features
            dead_features = (feature_frequencies == 0).sum().item()
            
            # Gini coefficient for feature usage
            sorted_freqs, _ = torch.sort(feature_frequencies)
            n = len(sorted_freqs)
            index = torch.arange(1, n + 1, dtype=torch.float)
            gini = (2 * (sorted_freqs * index).sum()) / (n * sorted_freqs.sum()) - (n + 1) / n
            
            metrics = {
                'overall_sparsity': overall_sparsity,
                'l0_norm_mean': l0_norm.mean().item(),
                'l0_norm_std': l0_norm.std().item(),
                'l0_norm_median': l0_norm.median().item(),
                'dead_features_count': dead_features,
                'dead_features_fraction': dead_features / feature_acts.shape[1],
                'feature_frequency_mean': feature_frequencies.mean().item(),
                'feature_frequency_std': feature_frequencies.std().item(),
                'gini_coefficient': gini.item(),
            }
            
        return metrics
        
    def evaluate_sae_features(
        self,
        sae: SAE,
        test_activations: Tensor,
    ) -> Dict[str, float]:
        """
        Evaluate SAE feature quality and interpretability.
        
        Args:
            sae: SAE model to evaluate
            test_activations: Test activations
            
        Returns:
            Dictionary of feature metrics
        """
        logger.info("Evaluating SAE features")
        
        sae.eval()
        with torch.no_grad():
            output = sae(test_activations)
            feature_acts = output.feature_acts
            
            # Feature activation statistics
            feature_means = feature_acts.mean(dim=0)
            feature_stds = feature_acts.std(dim=0)
            feature_maxes = feature_acts.max(dim=0)[0]
            
            # Feature diversity (how different features are from each other)
            feature_correlations = torch.corrcoef(feature_acts.T)
            feature_correlations = torch.nan_to_num(feature_correlations, nan=0.0)
            
            # Remove diagonal and compute mean absolute correlation
            mask = ~torch.eye(feature_correlations.shape[0], dtype=bool)
            mean_abs_correlation = feature_correlations[mask].abs().mean()
            
            # Feature stability (consistency across samples)
            feature_cv = feature_stds / (feature_means + 1e-8)  # Coefficient of variation
            
            metrics = {
                'feature_mean_avg': feature_means.mean().item(),
                'feature_std_avg': feature_stds.mean().item(),
                'feature_max_avg': feature_maxes.mean().item(),
                'mean_abs_correlation': mean_abs_correlation.item(),
                'feature_cv_mean': feature_cv.mean().item(),
                'feature_cv_std': feature_cv.std().item(),
                'num_features': feature_acts.shape[1],
            }
            
        return metrics
        
    def evaluate_sae_comprehensive(
        self,
        sae: SAE,
        test_activations: Tensor,
        weights: Optional[Dict[str, float]] = None,
    ) -> SAEEvaluationResult:
        """
        Comprehensive SAE evaluation.
        
        Args:
            sae: SAE model to evaluate
            test_activations: Test activations
            weights: Weights for different metric categories
            
        Returns:
            Comprehensive evaluation result
        """
        if weights is None:
            weights = {
                'reconstruction': 0.4,
                'sparsity': 0.3,
                'features': 0.3,
            }
            
        logger.info("Running comprehensive SAE evaluation")
        
        # Evaluate different aspects
        reconstruction_metrics = self.evaluate_sae_reconstruction(sae, test_activations)
        sparsity_metrics = self.evaluate_sae_sparsity(sae, test_activations)
        feature_metrics = self.evaluate_sae_features(sae, test_activations)
        
        # Compute overall score
        reconstruction_score = 1 - reconstruction_metrics['fvu']  # Higher is better
        sparsity_score = sparsity_metrics['overall_sparsity']  # Higher is better
        feature_score = 1 - sparsity_metrics['dead_features_fraction']  # Higher is better
        
        overall_score = (
            weights['reconstruction'] * reconstruction_score +
            weights['sparsity'] * sparsity_score +
            weights['features'] * feature_score
        )
        
        result = SAEEvaluationResult(
            reconstruction_metrics=reconstruction_metrics,
            sparsity_metrics=sparsity_metrics,
            feature_metrics=feature_metrics,
            overall_score=overall_score,
            metadata={
                'test_samples': test_activations.shape[0],
                'input_dim': test_activations.shape[1],
                'sae_dim': sae.d_sae,
                'expansion_factor': sae.d_sae / sae.d_in,
                'weights': weights,
            }
        )
        
        # Store in history
        self.evaluation_history.append(result)
        
        logger.info(f"SAE evaluation completed - Overall score: {overall_score:.4f}")
        return result
        
    def compare_saes(
        self,
        saes: List[SAE],
        test_activations: Tensor,
        sae_names: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """
        Compare multiple SAE models.
        
        Args:
            saes: List of SAE models to compare
            test_activations: Test activations
            sae_names: Optional names for the SAEs
            
        Returns:
            Comparison results
        """
        if sae_names is None:
            sae_names = [f"SAE_{i}" for i in range(len(saes))]
            
        logger.info(f"Comparing {len(saes)} SAE models")
        
        results = {}
        for sae, name in zip(saes, sae_names):
            results[name] = self.evaluate_sae_comprehensive(sae, test_activations)
            
        # Create comparison summary
        comparison = {
            'individual_results': results,
            'summary': {},
        }
        
        # Compare key metrics
        metrics_to_compare = [
            'overall_score',
            'reconstruction_metrics.fvu',
            'sparsity_metrics.overall_sparsity',
            'sparsity_metrics.dead_features_fraction',
        ]
        
        for metric in metrics_to_compare:
            values = []
            for name in sae_names:
                result = results[name]
                if '.' in metric:
                    parts = metric.split('.')
                    value = getattr(result, parts[0])[parts[1]]
                else:
                    value = getattr(result, metric)
                values.append(value)
                
            comparison['summary'][metric] = {
                'values': dict(zip(sae_names, values)),
                'best': sae_names[np.argmax(values) if 'fvu' not in metric and 'dead' not in metric else np.argmin(values)],
                'worst': sae_names[np.argmin(values) if 'fvu' not in metric and 'dead' not in metric else np.argmax(values)],
            }
            
        return comparison
        
    def get_evaluation_summary(self) -> Dict[str, Any]:
        """
        Get summary of all evaluations performed.
        
        Returns:
            Summary of evaluation history
        """
        if not self.evaluation_history:
            return {'message': 'No evaluations performed yet'}
            
        scores = [result.overall_score for result in self.evaluation_history]
        
        return {
            'num_evaluations': len(self.evaluation_history),
            'score_statistics': {
                'mean': np.mean(scores),
                'std': np.std(scores),
                'min': np.min(scores),
                'max': np.max(scores),
            },
            'latest_score': scores[-1],
            'best_score': np.max(scores),
            'worst_score': np.min(scores),
        }
