"""
Activation analyzer for SAE feature analysis.

This module provides tools for analyzing activation patterns, feature
distributions, and model behavior through SAE features.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
import torch
import numpy as np
from torch import Tensor
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ActivationStats:
    """Statistics for activation analysis."""
    
    mean: Tensor
    std: Tensor
    min_val: Tensor
    max_val: Tensor
    sparsity: Tensor
    active_fraction: Tensor
    percentiles: Dict[int, Tensor]


class ActivationAnalyzer:
    """
    Analyzer for SAE activations and feature patterns.
    
    Provides comprehensive analysis of activation patterns, feature
    importance, and model behavior through SAE representations.
    """
    
    def __init__(self, device: str = "cpu"):
        """
        Initialize activation analyzer.
        
        Args:
            device: Device for computation
        """
        self.device = device
        self.activation_history = []
        self.feature_stats = {}
        
    def analyze_activations(
        self,
        activations: Tensor,
        feature_names: Optional[List[str]] = None,
        compute_correlations: bool = True,
    ) -> Dict[str, Any]:
        """
        Comprehensive analysis of activation patterns.
        
        Args:
            activations: Activation tensor [batch_size, num_features]
            feature_names: Optional names for features
            compute_correlations: Whether to compute feature correlations
            
        Returns:
            Dictionary of analysis results
        """
        logger.info(f"Analyzing activations: {activations.shape}")
        
        # Basic statistics
        stats = self._compute_basic_stats(activations)
        
        # Sparsity analysis
        sparsity_analysis = self._analyze_sparsity(activations)
        
        # Feature importance
        importance_analysis = self._analyze_feature_importance(activations)
        
        # Correlation analysis
        correlation_analysis = {}
        if compute_correlations:
            correlation_analysis = self._analyze_correlations(activations)
            
        # Distribution analysis
        distribution_analysis = self._analyze_distributions(activations)
        
        results = {
            'basic_stats': stats,
            'sparsity': sparsity_analysis,
            'importance': importance_analysis,
            'correlations': correlation_analysis,
            'distributions': distribution_analysis,
            'feature_names': feature_names,
            'shape': activations.shape,
        }
        
        # Store for history
        self.activation_history.append(results)
        
        return results
        
    def _compute_basic_stats(self, activations: Tensor) -> ActivationStats:
        """Compute basic activation statistics."""
        with torch.no_grad():
            # Basic statistics
            mean = activations.mean(dim=0)
            std = activations.std(dim=0)
            min_val = activations.min(dim=0)[0]
            max_val = activations.max(dim=0)[0]
            
            # Sparsity metrics
            sparsity = (activations == 0).float().mean(dim=0)
            active_fraction = (activations > 0).float().mean(dim=0)
            
            # Percentiles
            percentiles = {}
            for p in [10, 25, 50, 75, 90, 95, 99]:
                percentiles[p] = torch.quantile(activations, p/100, dim=0)
                
        return ActivationStats(
            mean=mean,
            std=std,
            min_val=min_val,
            max_val=max_val,
            sparsity=sparsity,
            active_fraction=active_fraction,
            percentiles=percentiles,
        )
        
    def _analyze_sparsity(self, activations: Tensor) -> Dict[str, Any]:
        """Analyze sparsity patterns."""
        with torch.no_grad():
            # Overall sparsity
            overall_sparsity = (activations == 0).float().mean().item()
            
            # Per-sample sparsity
            sample_sparsity = (activations == 0).float().mean(dim=1)
            
            # Per-feature sparsity
            feature_sparsity = (activations == 0).float().mean(dim=0)
            
            # Active feature counts per sample
            active_counts = (activations > 0).sum(dim=1)
            
            # Dead features (never active)
            dead_features = (activations.sum(dim=0) == 0).sum().item()
            
            return {
                'overall_sparsity': overall_sparsity,
                'sample_sparsity_mean': sample_sparsity.mean().item(),
                'sample_sparsity_std': sample_sparsity.std().item(),
                'feature_sparsity_mean': feature_sparsity.mean().item(),
                'feature_sparsity_std': feature_sparsity.std().item(),
                'active_features_per_sample_mean': active_counts.float().mean().item(),
                'active_features_per_sample_std': active_counts.float().std().item(),
                'dead_features_count': dead_features,
                'dead_features_fraction': dead_features / activations.shape[1],
            }
            
    def _analyze_feature_importance(self, activations: Tensor) -> Dict[str, Any]:
        """Analyze feature importance patterns."""
        with torch.no_grad():
            # Feature activation magnitudes
            feature_magnitudes = activations.abs().mean(dim=0)
            
            # Feature activation frequencies
            feature_frequencies = (activations > 0).float().mean(dim=0)
            
            # Combined importance score
            importance_scores = feature_magnitudes * feature_frequencies
            
            # Top features
            top_k = min(20, activations.shape[1])
            top_indices = torch.topk(importance_scores, top_k)[1]
            
            return {
                'feature_magnitudes': feature_magnitudes,
                'feature_frequencies': feature_frequencies,
                'importance_scores': importance_scores,
                'top_features': top_indices,
                'importance_distribution': {
                    'mean': importance_scores.mean().item(),
                    'std': importance_scores.std().item(),
                    'max': importance_scores.max().item(),
                    'min': importance_scores.min().item(),
                }
            }
            
    def _analyze_correlations(self, activations: Tensor) -> Dict[str, Any]:
        """Analyze feature correlations."""
        with torch.no_grad():
            # Compute correlation matrix
            activations_centered = activations - activations.mean(dim=0)
            correlation_matrix = torch.corrcoef(activations_centered.T)
            
            # Handle NaN values (from zero-variance features)
            correlation_matrix = torch.nan_to_num(correlation_matrix, nan=0.0)
            
            # Correlation statistics
            # Remove diagonal (self-correlations)
            mask = ~torch.eye(correlation_matrix.shape[0], dtype=bool)
            off_diagonal_corrs = correlation_matrix[mask]
            
            # High correlation pairs
            high_corr_threshold = 0.8
            high_corr_mask = correlation_matrix.abs() > high_corr_threshold
            high_corr_mask.fill_diagonal_(False)  # Remove diagonal
            high_corr_pairs = torch.nonzero(high_corr_mask)
            
            return {
                'correlation_matrix': correlation_matrix,
                'mean_correlation': off_diagonal_corrs.mean().item(),
                'std_correlation': off_diagonal_corrs.std().item(),
                'max_correlation': off_diagonal_corrs.max().item(),
                'min_correlation': off_diagonal_corrs.min().item(),
                'high_correlation_pairs': high_corr_pairs,
                'num_high_correlation_pairs': len(high_corr_pairs),
            }
            
    def _analyze_distributions(self, activations: Tensor) -> Dict[str, Any]:
        """Analyze activation value distributions."""
        with torch.no_grad():
            # Only analyze non-zero activations
            nonzero_activations = activations[activations > 0]
            
            if len(nonzero_activations) == 0:
                return {'error': 'No non-zero activations found'}
                
            # Distribution statistics
            log_activations = torch.log(nonzero_activations + 1e-8)
            
            return {
                'nonzero_count': len(nonzero_activations),
                'nonzero_mean': nonzero_activations.mean().item(),
                'nonzero_std': nonzero_activations.std().item(),
                'nonzero_median': nonzero_activations.median().item(),
                'log_mean': log_activations.mean().item(),
                'log_std': log_activations.std().item(),
                'value_range': (nonzero_activations.min().item(), nonzero_activations.max().item()),
            }
            
    def compare_activations(
        self,
        activations_a: Tensor,
        activations_b: Tensor,
        labels: Tuple[str, str] = ("A", "B"),
    ) -> Dict[str, Any]:
        """
        Compare two sets of activations.
        
        Args:
            activations_a: First set of activations
            activations_b: Second set of activations
            labels: Labels for the two sets
            
        Returns:
            Comparison results
        """
        logger.info(f"Comparing activations: {labels[0]} vs {labels[1]}")
        
        # Analyze both sets
        analysis_a = self.analyze_activations(activations_a, compute_correlations=False)
        analysis_b = self.analyze_activations(activations_b, compute_correlations=False)
        
        # Compute differences
        with torch.no_grad():
            # Sparsity differences
            sparsity_diff = (
                analysis_b['sparsity']['overall_sparsity'] - 
                analysis_a['sparsity']['overall_sparsity']
            )
            
            # Feature importance differences
            importance_diff = (
                analysis_b['importance']['importance_scores'] - 
                analysis_a['importance']['importance_scores']
            )
            
            # Statistical differences
            mean_diff = analysis_b['basic_stats'].mean - analysis_a['basic_stats'].mean
            std_diff = analysis_b['basic_stats'].std - analysis_a['basic_stats'].std
            
        return {
            'analysis_a': analysis_a,
            'analysis_b': analysis_b,
            'differences': {
                'sparsity_diff': sparsity_diff,
                'importance_diff': importance_diff,
                'mean_diff': mean_diff,
                'std_diff': std_diff,
            },
            'labels': labels,
        }
        
    def get_feature_summary(self, feature_idx: int) -> Dict[str, Any]:
        """
        Get summary for a specific feature across all analyzed activations.
        
        Args:
            feature_idx: Index of the feature to summarize
            
        Returns:
            Feature summary across all analyses
        """
        if not self.activation_history:
            return {'error': 'No activation history available'}
            
        summary = {
            'feature_idx': feature_idx,
            'analyses': [],
        }
        
        for i, analysis in enumerate(self.activation_history):
            if feature_idx < analysis['shape'][1]:
                feature_summary = {
                    'analysis_idx': i,
                    'mean': analysis['basic_stats'].mean[feature_idx].item(),
                    'std': analysis['basic_stats'].std[feature_idx].item(),
                    'sparsity': analysis['basic_stats'].sparsity[feature_idx].item(),
                    'importance': analysis['importance']['importance_scores'][feature_idx].item(),
                }
                summary['analyses'].append(feature_summary)
                
        return summary
        
    def clear_history(self) -> None:
        """Clear activation analysis history."""
        self.activation_history.clear()
        logger.info("Cleared activation analysis history")
