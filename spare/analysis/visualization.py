"""
Visualization tools for SAE analysis.

This module provides plotting and visualization utilities for SAE features,
activations, and evaluation results.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
import torch
from torch import Tensor
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

logger = logging.getLogger(__name__)


class SAEVisualizer:
    """
    Visualization tools for SAE analysis and results.
    
    Provides plotting functions for activation patterns, feature distributions,
    evaluation metrics, and comparison results.
    """
    
    def __init__(self, style: str = "seaborn-v0_8", figsize: Tuple[int, int] = (10, 6)):
        """
        Initialize SAE visualizer.
        
        Args:
            style: Matplotlib style to use
            figsize: Default figure size
        """
        try:
            plt.style.use(style)
        except:
            logger.warning(f"Style {style} not available, using default")
            
        self.default_figsize = figsize
        
    def plot_activation_distribution(
        self,
        activations: Tensor,
        title: str = "Activation Distribution",
        save_path: Optional[str] = None,
    ) -> plt.Figure:
        """
        Plot distribution of activation values.
        
        Args:
            activations: Activation tensor
            title: Plot title
            save_path: Optional path to save figure
            
        Returns:
            Matplotlib figure
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle(title, fontsize=16)
        
        # Convert to numpy
        acts = activations.detach().cpu().numpy()
        
        # Overall distribution
        axes[0, 0].hist(acts.flatten(), bins=50, alpha=0.7, density=True)
        axes[0, 0].set_title("Overall Activation Distribution")
        axes[0, 0].set_xlabel("Activation Value")
        axes[0, 0].set_ylabel("Density")
        
        # Non-zero activations only
        nonzero_acts = acts[acts > 0]
        if len(nonzero_acts) > 0:
            axes[0, 1].hist(nonzero_acts, bins=50, alpha=0.7, density=True)
            axes[0, 1].set_title("Non-zero Activation Distribution")
            axes[0, 1].set_xlabel("Activation Value")
            axes[0, 1].set_ylabel("Density")
        
        # Sparsity per sample
        sparsity_per_sample = (acts == 0).mean(axis=1)
        axes[1, 0].hist(sparsity_per_sample, bins=30, alpha=0.7)
        axes[1, 0].set_title("Sparsity per Sample")
        axes[1, 0].set_xlabel("Fraction of Zero Activations")
        axes[1, 0].set_ylabel("Count")
        
        # Feature activation frequency
        feature_freq = (acts > 0).mean(axis=0)
        axes[1, 1].hist(feature_freq, bins=30, alpha=0.7)
        axes[1, 1].set_title("Feature Activation Frequency")
        axes[1, 1].set_xlabel("Activation Frequency")
        axes[1, 1].set_ylabel("Count")
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            
        return fig
        
    def plot_feature_importance(
        self,
        importance_scores: Tensor,
        top_k: int = 20,
        title: str = "Feature Importance",
        save_path: Optional[str] = None,
    ) -> plt.Figure:
        """
        Plot feature importance scores.
        
        Args:
            importance_scores: Feature importance tensor
            top_k: Number of top features to show
            title: Plot title
            save_path: Optional path to save figure
            
        Returns:
            Matplotlib figure
        """
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle(title, fontsize=16)
        
        scores = importance_scores.detach().cpu().numpy()
        
        # Top-k features
        top_indices = np.argsort(scores)[-top_k:]
        top_scores = scores[top_indices]
        
        axes[0].barh(range(top_k), top_scores)
        axes[0].set_yticks(range(top_k))
        axes[0].set_yticklabels([f"Feature {i}" for i in top_indices])
        axes[0].set_xlabel("Importance Score")
        axes[0].set_title(f"Top {top_k} Features")
        
        # Overall distribution
        axes[1].hist(scores, bins=50, alpha=0.7, density=True)
        axes[1].set_xlabel("Importance Score")
        axes[1].set_ylabel("Density")
        axes[1].set_title("Importance Score Distribution")
        axes[1].axvline(scores.mean(), color='red', linestyle='--', label='Mean')
        axes[1].legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            
        return fig
        
    def plot_reconstruction_quality(
        self,
        original: Tensor,
        reconstructed: Tensor,
        title: str = "Reconstruction Quality",
        save_path: Optional[str] = None,
    ) -> plt.Figure:
        """
        Plot reconstruction quality comparison.
        
        Args:
            original: Original activations
            reconstructed: Reconstructed activations
            title: Plot title
            save_path: Optional path to save figure
            
        Returns:
            Matplotlib figure
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle(title, fontsize=16)
        
        orig = original.detach().cpu().numpy()
        recon = reconstructed.detach().cpu().numpy()
        
        # Scatter plot
        sample_indices = np.random.choice(orig.size, min(10000, orig.size), replace=False)
        axes[0, 0].scatter(orig.flat[sample_indices], recon.flat[sample_indices], alpha=0.5, s=1)
        axes[0, 0].plot([orig.min(), orig.max()], [orig.min(), orig.max()], 'r--')
        axes[0, 0].set_xlabel("Original")
        axes[0, 0].set_ylabel("Reconstructed")
        axes[0, 0].set_title("Original vs Reconstructed")
        
        # Residuals
        residuals = recon - orig
        axes[0, 1].hist(residuals.flatten(), bins=50, alpha=0.7, density=True)
        axes[0, 1].set_xlabel("Residual (Recon - Orig)")
        axes[0, 1].set_ylabel("Density")
        axes[0, 1].set_title("Reconstruction Residuals")
        axes[0, 1].axvline(0, color='red', linestyle='--')
        
        # Per-dimension MSE
        per_dim_mse = ((recon - orig) ** 2).mean(axis=0)
        axes[1, 0].plot(per_dim_mse)
        axes[1, 0].set_xlabel("Dimension")
        axes[1, 0].set_ylabel("MSE")
        axes[1, 0].set_title("Per-dimension MSE")
        
        # Per-sample MSE
        per_sample_mse = ((recon - orig) ** 2).mean(axis=1)
        axes[1, 1].hist(per_sample_mse, bins=30, alpha=0.7)
        axes[1, 1].set_xlabel("MSE")
        axes[1, 1].set_ylabel("Count")
        axes[1, 1].set_title("Per-sample MSE Distribution")
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            
        return fig
        
    def plot_evaluation_metrics(
        self,
        evaluation_results: Dict[str, Any],
        title: str = "SAE Evaluation Metrics",
        save_path: Optional[str] = None,
    ) -> plt.Figure:
        """
        Plot evaluation metrics summary.
        
        Args:
            evaluation_results: Results from SAE evaluation
            title: Plot title
            save_path: Optional path to save figure
            
        Returns:
            Matplotlib figure
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle(title, fontsize=16)
        
        # Reconstruction metrics
        recon_metrics = evaluation_results.get('reconstruction_metrics', {})
        if recon_metrics:
            metrics = ['mse', 'mae', 'fvu', 'cosine_similarity']
            values = [recon_metrics.get(m, 0) for m in metrics]
            
            axes[0, 0].bar(metrics, values)
            axes[0, 0].set_title("Reconstruction Metrics")
            axes[0, 0].tick_params(axis='x', rotation=45)
        
        # Sparsity metrics
        sparsity_metrics = evaluation_results.get('sparsity_metrics', {})
        if sparsity_metrics:
            metrics = ['overall_sparsity', 'l0_norm_mean', 'dead_features_fraction']
            values = [sparsity_metrics.get(m, 0) for m in metrics]
            
            axes[0, 1].bar(metrics, values)
            axes[0, 1].set_title("Sparsity Metrics")
            axes[0, 1].tick_params(axis='x', rotation=45)
        
        # Feature metrics
        feature_metrics = evaluation_results.get('feature_metrics', {})
        if feature_metrics:
            metrics = ['feature_mean_avg', 'feature_std_avg', 'mean_abs_correlation']
            values = [feature_metrics.get(m, 0) for m in metrics]
            
            axes[1, 0].bar(metrics, values)
            axes[1, 0].set_title("Feature Metrics")
            axes[1, 0].tick_params(axis='x', rotation=45)
        
        # Overall score
        overall_score = evaluation_results.get('overall_score', 0)
        axes[1, 1].bar(['Overall Score'], [overall_score])
        axes[1, 1].set_title("Overall SAE Score")
        axes[1, 1].set_ylim(0, 1)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            
        return fig
        
    def plot_sae_comparison(
        self,
        comparison_results: Dict[str, Any],
        title: str = "SAE Comparison",
        save_path: Optional[str] = None,
    ) -> plt.Figure:
        """
        Plot comparison between multiple SAEs.
        
        Args:
            comparison_results: Results from SAE comparison
            title: Plot title
            save_path: Optional path to save figure
            
        Returns:
            Matplotlib figure
        """
        summary = comparison_results.get('summary', {})
        if not summary:
            logger.warning("No summary data found in comparison results")
            return plt.figure()
            
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(title, fontsize=16)
        
        # Plot key metrics
        metrics_to_plot = [
            ('overall_score', 'Overall Score'),
            ('reconstruction_metrics.fvu', 'Fraction Variance Unexplained'),
            ('sparsity_metrics.overall_sparsity', 'Overall Sparsity'),
            ('sparsity_metrics.dead_features_fraction', 'Dead Features Fraction'),
        ]
        
        for i, (metric, label) in enumerate(metrics_to_plot):
            if metric in summary:
                values = summary[metric]['values']
                names = list(values.keys())
                scores = list(values.values())
                
                ax = axes[i // 2, i % 2]
                bars = ax.bar(names, scores)
                ax.set_title(label)
                ax.tick_params(axis='x', rotation=45)
                
                # Highlight best performer
                best_idx = names.index(summary[metric]['best'])
                bars[best_idx].set_color('green')
                bars[best_idx].set_alpha(0.8)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            
        return fig
        
    def plot_training_progress(
        self,
        training_metrics: List[Dict[str, float]],
        title: str = "Training Progress",
        save_path: Optional[str] = None,
    ) -> plt.Figure:
        """
        Plot SAE training progress.
        
        Args:
            training_metrics: List of training metrics over time
            title: Plot title
            save_path: Optional path to save figure
            
        Returns:
            Matplotlib figure
        """
        if not training_metrics:
            logger.warning("No training metrics provided")
            return plt.figure()
            
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(title, fontsize=16)
        
        # Extract metrics
        steps = [m.get('step', i) for i, m in enumerate(training_metrics)]
        
        metrics_to_plot = [
            ('total_loss', 'Total Loss'),
            ('mse_loss', 'MSE Loss'),
            ('l1_loss', 'L1 Loss'),
            ('sparsity', 'Sparsity'),
        ]
        
        for i, (metric, label) in enumerate(metrics_to_plot):
            values = [m.get(metric, 0) for m in training_metrics]
            
            ax = axes[i // 2, i % 2]
            ax.plot(steps, values)
            ax.set_xlabel('Training Step')
            ax.set_ylabel(label)
            ax.set_title(f'{label} over Training')
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            
        return fig
        
    def save_all_plots(
        self,
        plots: Dict[str, plt.Figure],
        output_dir: str,
        format: str = 'png',
    ) -> None:
        """
        Save multiple plots to directory.
        
        Args:
            plots: Dictionary of plot names to figures
            output_dir: Output directory
            format: Image format
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        for name, fig in plots.items():
            save_path = output_path / f"{name}.{format}"
            fig.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Saved plot: {save_path}")
            
        logger.info(f"Saved {len(plots)} plots to {output_dir}")
