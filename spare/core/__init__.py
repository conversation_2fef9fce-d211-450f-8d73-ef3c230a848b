"""
Core components for SpARE (Sparse Auto-Encoder based Representation Engineering)

This module contains the main classes and functions for SAE training and inference,
designed to work with any HuggingFace transformer model.
"""

from .config import SAEConfig, TrainingConfig, ModelConfig, DatasetConfig
from .sae import SAE, TrainingSAE
from .trainer import SAETrainer
from .model_loader import UniversalModelLoader
from .dataset_manager import DatasetManager
from .activations_store import ActivationsStore

__all__ = [
    # Configuration
    "SAEConfig",
    "TrainingConfig", 
    "ModelConfig",
    "DatasetConfig",
    
    # Core SAE classes
    "SAE",
    "TrainingSAE",
    "SAETrainer",
    
    # Utilities
    "UniversalModelLoader",
    "DatasetManager", 
    "ActivationsStore",
]
