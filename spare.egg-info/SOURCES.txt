LICENSE
README.md
setup.py
spare/__init__.py
spare/eval_utils.py
spare/group_prompts.py
spare/kc_probing.py
spare/mutual_information_and_expectation.py
spare/patch_utils.py
spare/prepare_eval.py
spare/sae_repe_utils.py
spare/save_grouped_activations.py
spare/spare_for_generation.py
spare/utils.py
spare.egg-info/PKG-INFO
spare.egg-info/SOURCES.txt
spare.egg-info/dependency_links.txt
spare.egg-info/top_level.txt
spare/analysis/__init__.py
spare/analysis/activation_analyzer.py
spare/analysis/activation_patterns.py
spare/analysis/analysis_save_activations.py
spare/analysis/evaluation.py
spare/analysis/function_extractor.py
spare/analysis/group_instance.py
spare/analysis/representation_engineer.py
spare/analysis/visualization.py
spare/core/__init__.py
spare/core/activations_store.py
spare/core/config.py
spare/core/dataset_manager.py
spare/core/model_loader.py
spare/core/sae.py
spare/core/trainer.py
spare/datasets/__init__.py
spare/datasets/eval_datasets_macnoise.py
spare/datasets/eval_datasets_nqswap.py
spare/datasets/function_extraction_datasets.py
spare/function_extraction_modellings/__init__.py
spare/function_extraction_modellings/function_extraction_gemma2.py
spare/function_extraction_modellings/function_extraction_llama.py
spare/function_extraction_modellings/function_extractor.py
spare/sae/__init__.py
spare/sae/__main__.py
spare/sae/config.py
spare/sae/data.py
spare/sae/kernels.py
spare/sae/sae.py
spare/sae/trainer.py
spare/sae/utils.py
spare/sae_lens/__init__.py
spare/sae_lens/cache_activations_runner.py
spare/sae_lens/config.py
spare/sae_lens/eleuther_sae_wrapper.py
spare/sae_lens/evals.py
spare/sae_lens/load_model.py
spare/sae_lens/pretokenize_runner.py
spare/sae_lens/sae.py
spare/sae_lens/sae_training_runner.py
spare/sae_lens/tokenization_and_batching.py
spare/sae_lens/toy_model_runner.py
spare/sae_lens/analysis/__init__.py
spare/sae_lens/analysis/feature_statistics.py
spare/sae_lens/analysis/hooked_sae_transformer.py
spare/sae_lens/analysis/neuronpedia_integration.py
spare/sae_lens/analysis/tsea.py
spare/sae_lens/toolkit/__init__.py
spare/sae_lens/toolkit/pretrained_sae_loaders.py
spare/sae_lens/toolkit/pretrained_saes.py
spare/sae_lens/toolkit/pretrained_saes_directory.py
spare/sae_lens/training/__init__.py
spare/sae_lens/training/activations_store.py
spare/sae_lens/training/geometric_median.py
spare/sae_lens/training/optim.py
spare/sae_lens/training/sae_trainer.py
spare/sae_lens/training/toy_models.py
spare/sae_lens/training/train_toy_sae.py
spare/sae_lens/training/training_sae.py
spare/sae_lens/training/upload_saes_to_huggingface.py