"""
SpARE Demo: Comprehensive demonstration of the refactored SAE library.

This demo showcases the new generalized functionality that works with any
HuggingFace model and dataset, demonstrating the improved API and capabilities.
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional
import torch
import numpy as np
from pathlib import Path

# Import the new SpARE library
import spare
from spare.core.config import SAEConfig, ModelConfig, DatasetConfig, TrainingConfig
from spare.core.model_loader import UniversalModelLoader
from spare.core.dataset_manager import DatasetManager
from spare.core.trainer import SAETrainer
from spare.analysis.function_extractor import FunctionExtractor

# Setup environment and logging
os.environ["TOKENIZERS_PARALLELISM"] = "false"
logger = spare.setup_logging(level=logging.INFO)


def create_sae_config(
    model_name: str = "microsoft/DialoGPT-medium",
    dataset_name: str = "wikitext",
    hook_layer: int = 6,
    expansion_factor: int = 32,
    architecture: str = "standard",
) -> SAEConfig:
    """
    Create a comprehensive SAE configuration for any HuggingFace model.

    Args:
        model_name: HuggingFace model identifier
        dataset_name: HuggingFace dataset identifier
        hook_layer: Layer to extract activations from
        expansion_factor: SAE expansion factor
        architecture: SAE architecture type

    Returns:
        Complete SAE configuration
    """
    logger.info(f"Creating SAE config for model: {model_name}")

    # Model configuration
    model_config = ModelConfig(
        model_name=model_name,
        use_flash_attention=True,
        torch_dtype="bfloat16",
        trust_remote_code=False,
    )

    # Dataset configuration
    dataset_config = DatasetConfig(
        dataset_name=dataset_name,
        dataset_split="train",
        text_column="text",
        max_seq_length=2048,
        chunk_size=2048,
        streaming=False,
    )

    # Training configuration
    training_config = TrainingConfig(
        total_training_tokens=1_000_000,  # Smaller for demo
        batch_size=2048,
        learning_rate=3e-4,
        l1_coefficient=1e-3,
        lr_scheduler="cosine",
        log_every_n_steps=50,
        eval_every_n_tokens=50_000,
        checkpoint_every_n_tokens=100_000,
        use_wandb=False,  # Disable for demo
    )

    # Main SAE configuration
    config = SAEConfig(
        model=model_config,
        dataset=dataset_config,
        training=training_config,
        architecture=architecture,
        expansion_factor=expansion_factor,
        hook_layer=hook_layer,
        hook_name="model.layers.{layer}",  # Will be formatted with layer number
        activation_fn="relu",
        normalize_decoder=True,
        prepend_bos=True,
        device="cuda" if torch.cuda.is_available() else "cpu",
        dtype="float32",
        seed=42,
    )

    return config


def train_sae_on_model(config: SAEConfig) -> spare.core.sae.TrainingSAE:
    """
    Train an SAE on any HuggingFace model using the new generalized framework.

    Args:
        config: SAE configuration

    Returns:
        Trained SAE model
    """
    logger.info("Starting SAE training with new generalized framework")

    # Validate configuration
    issues = spare.validate_config(config)
    if issues:
        logger.error(f"Configuration issues: {issues}")
        raise ValueError(f"Invalid configuration: {issues}")

    # Initialize trainer
    trainer = SAETrainer(config)

    # Train the SAE
    trained_sae = trainer.train()

    logger.info("SAE training completed successfully")
    return trained_sae


def demonstrate_universal_model_support():
    """
    Demonstrate that the new framework works with various HuggingFace models.
    """
    logger.info("Demonstrating universal model support")

    # Test different model architectures
    test_models = [
        "microsoft/DialoGPT-medium",  # GPT-2 based
        "distilbert-base-uncased",  # BERT based
        "google/flan-t5-small",  # T5 based
        "facebook/opt-125m",  # OPT based
    ]

    for model_name in test_models:
        try:
            logger.info(f"Testing model: {model_name}")

            # Create configuration
            config = create_sae_config(
                model_name=model_name,
                dataset_name="wikitext",
                hook_layer=2,  # Use early layer for small models
                expansion_factor=8,  # Smaller for demo
            )

            # Load model and get info
            model_loader = UniversalModelLoader(config.model)
            model, tokenizer = model_loader.load_model_and_tokenizer()

            # Get model information
            model_info = model_loader.get_model_info()
            logger.info(f"Model info: {model_info}")

            # Get available hook names
            hook_names = model_loader.get_hook_names()
            logger.info(f"Available hooks: {list(hook_names.keys())}")

            logger.info(f"✓ Successfully loaded {model_name}")

        except Exception as e:
            logger.warning(f"✗ Failed to load {model_name}: {e}")

    logger.info("Universal model support demonstration completed")


def demonstrate_flexible_dataset_support():
    """
    Demonstrate that the new framework works with various HuggingFace datasets.
    """
    logger.info("Demonstrating flexible dataset support")

    # Test different datasets
    test_datasets = [
        ("wikitext", "wikitext-2-raw-v1", "text"),
        ("openwebtext", "openwebtext", "text"),
        ("c4", "en", "text"),
        ("bookcorpus", "plain_text", "text"),
    ]

    for dataset_name, dataset_config, text_column in test_datasets:
        try:
            logger.info(f"Testing dataset: {dataset_name}")

            # Create dataset configuration
            dataset_config_obj = DatasetConfig(
                dataset_name=dataset_name,
                dataset_split="train",
                text_column=text_column,
                max_seq_length=512,  # Smaller for demo
                chunk_size=512,
                streaming=True,  # Use streaming for large datasets
            )

            # Create a simple tokenizer for testing
            from transformers import AutoTokenizer

            tokenizer = AutoTokenizer.from_pretrained("gpt2")
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token

            # Initialize dataset manager
            dataset_manager = DatasetManager(dataset_config_obj, tokenizer)

            # Load dataset
            dataset = dataset_manager.load_dataset()

            # Get dataset info
            dataset_info = dataset_manager.get_dataset_info()
            logger.info(f"Dataset info: {dataset_info}")

            logger.info(f"✓ Successfully loaded {dataset_name}")

        except Exception as e:
            logger.warning(f"✗ Failed to load {dataset_name}: {e}")

    logger.info("Flexible dataset support demonstration completed")


def demonstrate_sae_architectures():
    """
    Demonstrate different SAE architectures supported by the new framework.
    """
    logger.info("Demonstrating SAE architectures")

    # Test different SAE architectures
    architectures = ["standard", "gated", "jumprelu"]

    for architecture in architectures:
        try:
            logger.info(f"Testing SAE architecture: {architecture}")

            # Create configuration for this architecture
            config = create_sae_config(
                model_name="microsoft/DialoGPT-medium",
                dataset_name="wikitext",
                hook_layer=4,
                expansion_factor=8,
                architecture=architecture,
            )

            # Create SAE instance (without training for demo)
            from spare.core.sae import SAE

            sae = SAE(
                d_in=768,  # DialoGPT-medium hidden size
                d_sae=768 * 8,
                architecture=architecture,
                activation_fn="relu",
                normalize_decoder=True,
                device="cpu",  # Use CPU for demo
            )

            # Test forward pass
            test_input = torch.randn(10, 768)
            output = sae(test_input)

            logger.info(
                f"✓ {architecture} SAE - Input: {test_input.shape}, Output: {output.sae_out.shape}"
            )
            logger.info(
                f"  Sparsity: {output.sparsity.item():.4f}, FVU: {output.fvu.item():.4f}"
            )

        except Exception as e:
            logger.warning(f"✗ Failed to test {architecture} SAE: {e}")

    logger.info("SAE architectures demonstration completed")


def demonstrate_function_extraction():
    """
    Demonstrate the improved function extraction capabilities.
    """
    logger.info("Demonstrating function extraction")

    try:
        # Create a simple SAE for demonstration
        from spare.core.sae import SAE

        sae = SAE(
            d_in=512,
            d_sae=512 * 16,
            architecture="standard",
            activation_fn="relu",
            normalize_decoder=True,
            device="cpu",
        )

        # Create function extractor
        function_extractor = FunctionExtractor(
            sae=sae,
            initialization_method="uniform",
            regularization_strength=1e-5,
            device="cpu",
        )

        # Generate sample data
        context_activations = torch.randn(20, 512)
        target_activations = context_activations + 0.1 * torch.randn(20, 512)

        # Extract function
        result = function_extractor.extract_function(
            target_activations=target_activations,
            context_activations=context_activations,
            learning_rate=1e-2,
            num_iterations=100,
            verbose=False,
        )

        logger.info(f"✓ Function extraction completed")
        logger.info(f"  Active features: {len(result.active_features)}")
        logger.info(f"  Extraction strength: {result.extraction_strength:.6f}")
        logger.info(f"  Final loss: {result.metadata['final_loss']:.6f}")

        # Analyze feature importance
        importance_stats = function_extractor.analyze_feature_importance()
        logger.info(f"  Feature importance stats: {importance_stats}")

    except Exception as e:
        logger.warning(f"✗ Function extraction demonstration failed: {e}")

    logger.info("Function extraction demonstration completed")


def demonstrate_end_to_end_workflow():
    """
    Demonstrate a complete end-to-end workflow with the new framework.
    """
    logger.info("Demonstrating end-to-end workflow")

    try:
        # 1. Create configuration
        logger.info("Step 1: Creating SAE configuration")
        config = create_sae_config(
            model_name="microsoft/DialoGPT-medium",
            dataset_name="wikitext",
            hook_layer=6,
            expansion_factor=16,
            architecture="standard",
        )

        # 2. Validate configuration
        logger.info("Step 2: Validating configuration")
        issues = spare.validate_config(config)
        if issues:
            logger.warning(f"Configuration issues found: {issues}")
        else:
            logger.info("✓ Configuration is valid")

        # 3. Load model and tokenizer
        logger.info("Step 3: Loading model and tokenizer")
        model_loader = UniversalModelLoader(config.model)
        model, tokenizer = model_loader.load_model_and_tokenizer()
        model_info = model_loader.get_model_info()
        logger.info(f"✓ Loaded model: {model_info['model_name']}")

        # 4. Setup dataset
        logger.info("Step 4: Setting up dataset")
        dataset_manager = DatasetManager(config.dataset, tokenizer)
        dataset = dataset_manager.load_dataset()
        processed_dataset = dataset_manager.preprocess_dataset()
        dataset_info = dataset_manager.get_dataset_info()
        logger.info(f"✓ Processed dataset: {dataset_info}")

        # 5. Create and test SAE
        logger.info("Step 5: Creating SAE")
        from spare.core.sae import SAE

        sae = SAE(
            d_in=model_info["hidden_size"],
            d_sae=model_info["hidden_size"] * config.expansion_factor,
            architecture=config.architecture,
            activation_fn=config.activation_fn,
            normalize_decoder=config.normalize_decoder,
            device="cpu",  # Use CPU for demo
        )

        # Test SAE with sample data
        test_input = torch.randn(5, model_info["hidden_size"])
        output = sae(test_input)
        logger.info(f"✓ SAE test - Sparsity: {output.sparsity.item():.4f}")

        # 6. Demonstrate function extraction
        logger.info("Step 6: Testing function extraction")
        function_extractor = FunctionExtractor(
            sae=sae,
            device="cpu",
        )

        # Generate sample activations
        context_acts = torch.randn(10, model_info["hidden_size"])
        target_acts = context_acts + 0.05 * torch.randn(10, model_info["hidden_size"])

        # Extract function (quick demo)
        result = function_extractor.extract_function(
            target_activations=target_acts,
            context_activations=context_acts,
            learning_rate=1e-2,
            num_iterations=50,
            verbose=False,
        )

        logger.info(
            f"✓ Function extraction - Active features: {len(result.active_features)}"
        )

        logger.info("✓ End-to-end workflow completed successfully!")

        return {
            "config": config,
            "model_info": model_info,
            "dataset_info": dataset_info,
            "sae_output": output,
            "function_result": result,
        }

    except Exception as e:
        logger.error(f"✗ End-to-end workflow failed: {e}")
        return None


def run_comprehensive_demo():
    """
    Run a comprehensive demonstration of all SpARE capabilities.
    """
    logger.info("=" * 60)
    logger.info("SpARE Comprehensive Demo - Generalized SAE Framework")
    logger.info("=" * 60)

    # Demonstrate universal model support
    logger.info("\n" + "=" * 40)
    logger.info("1. Universal Model Support")
    logger.info("=" * 40)
    demonstrate_universal_model_support()

    # Demonstrate flexible dataset support
    logger.info("\n" + "=" * 40)
    logger.info("2. Flexible Dataset Support")
    logger.info("=" * 40)
    demonstrate_flexible_dataset_support()

    # Demonstrate SAE architectures
    logger.info("\n" + "=" * 40)
    logger.info("3. SAE Architectures")
    logger.info("=" * 40)
    demonstrate_sae_architectures()

    # Demonstrate function extraction
    logger.info("\n" + "=" * 40)
    logger.info("4. Function Extraction")
    logger.info("=" * 40)
    demonstrate_function_extraction()

    # Demonstrate end-to-end workflow
    logger.info("\n" + "=" * 40)
    logger.info("5. End-to-End Workflow")
    logger.info("=" * 40)
    result = demonstrate_end_to_end_workflow()

    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("Demo Summary")
    logger.info("=" * 60)

    if result:
        logger.info("✓ All demonstrations completed successfully!")
        logger.info(f"✓ Tested model: {result['model_info']['model_name']}")
        logger.info(f"✓ Processed dataset: {result['dataset_info']['dataset_name']}")
        logger.info(f"✓ SAE sparsity: {result['sae_output'].sparsity.item():.4f}")
        logger.info(
            f"✓ Function extraction: {len(result['function_result'].active_features)} active features"
        )
    else:
        logger.warning("Some demonstrations failed - check logs above")

    logger.info("\nThe new SpARE framework provides:")
    logger.info("• Universal HuggingFace model support")
    logger.info("• Flexible dataset handling")
    logger.info("• Multiple SAE architectures")
    logger.info("• Improved function extraction")
    logger.info("• Comprehensive configuration management")
    logger.info("• Better error handling and logging")

    logger.info("\n" + "=" * 60)
    logger.info("Demo completed!")
    logger.info("=" * 60)


def demonstrate_llama31_with_eleuther_sae():
    """
    Demonstrate using Llama 3.1 8B Instruct with EleutherAI SAE.
    """
    logger.info("=" * 60)
    logger.info("Llama 3.1 8B Instruct + EleutherAI SAE Demo")
    logger.info("=" * 60)

    try:
        # 1. Load Llama 3.1 8B Instruct model
        logger.info("Step 1: Loading Llama 3.1 8B Instruct model")

        model_config = ModelConfig(
            model_name="meta-llama/Meta-Llama-3.1-8B-Instruct",
            use_flash_attention=True,
            torch_dtype="bfloat16",
            device_map="auto",
            trust_remote_code=False,
        )

        model_loader = UniversalModelLoader(model_config)
        model, tokenizer = model_loader.load_model_and_tokenizer()
        model_info = model_loader.get_model_info()

        logger.info(f"✓ Loaded model: {model_info['model_name']}")
        logger.info(f"  Architecture: {model_info['architectures']}")
        logger.info(f"  Hidden size: {model_info['hidden_size']}")
        logger.info(f"  Num layers: {model_info['num_layers']}")
        logger.info(f"  Parameters: {model_info['total_parameters']:,}")

        # 2. Load EleutherAI SAE
        logger.info("\nStep 2: Loading EleutherAI SAE")

        # Import SAE loading utilities
        try:
            from sae_lens import SAE as EleutherSAE

            # Load the EleutherAI SAE for Llama 3.1 8B
            layer_idx = 16  # Middle layer for demonstration
            sae_id = f"blocks.{layer_idx}.hook_resid_post"

            logger.info(f"Loading SAE for layer {layer_idx}")
            eleuther_sae = EleutherSAE.from_pretrained(
                release="llama-3.1-8b-it-res-jb",
                sae_id=sae_id,
                device="cuda" if torch.cuda.is_available() else "cpu",
            )

            logger.info(f"✓ Loaded EleutherAI SAE")
            logger.info(f"  SAE ID: {sae_id}")
            logger.info(f"  Input dim: {eleuther_sae.cfg.d_in}")
            logger.info(f"  SAE dim: {eleuther_sae.cfg.d_sae}")
            logger.info(
                f"  Expansion factor: {eleuther_sae.cfg.d_sae / eleuther_sae.cfg.d_in:.1f}"
            )

        except ImportError:
            logger.warning("SAE-lens not available, using legacy loading method")
            # Fallback to legacy method
            sae = spare.load_frozen_sae(
                layer_idx=16, model_name="Meta-Llama-3.1-8B-Instruct"
            )
            logger.info("✓ Loaded SAE using legacy method")

        # 3. Test model with sample inputs
        logger.info("\nStep 3: Testing model with sample inputs")

        test_prompts = [
            "The capital of France is",
            "In machine learning, a neural network is",
            "The theory of relativity was developed by",
        ]

        for prompt in test_prompts:
            inputs = tokenizer(prompt, return_tensors="pt")
            input_ids = inputs["input_ids"].to(model.device)

            with torch.no_grad():
                outputs = model.generate(
                    input_ids,
                    max_new_tokens=10,
                    do_sample=False,
                    pad_token_id=tokenizer.eos_token_id,
                )

            generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
            logger.info(f"  Input: '{prompt}'")
            logger.info(f"  Output: '{generated_text}'")

        # 4. Extract activations and analyze with SAE
        logger.info("\nStep 4: Extracting activations and analyzing with SAE")

        # Create activations store
        from spare.core.activations_store import ActivationsStore
        from spare.core.dataset_manager import DatasetManager

        # Setup a small dataset for activation extraction
        dataset_config = DatasetConfig(
            dataset_name="wikitext",
            dataset_split="train",
            text_column="text",
            max_seq_length=512,
            chunk_size=512,
            streaming=False,
        )

        dataset_manager = DatasetManager(dataset_config, tokenizer)
        dataset = dataset_manager.load_dataset()
        processed_dataset = dataset_manager.preprocess_dataset()

        # Create SAE configuration for this model
        sae_config = SAEConfig(
            model=model_config,
            dataset=dataset_config,
            hook_layer=layer_idx,
            hook_name=f"model.layers.{layer_idx}",
            d_in=model_info["hidden_size"],
            expansion_factor=32,
            architecture="standard",
        )

        # Create activations store
        activations_store = ActivationsStore(
            model=model,
            tokenizer=tokenizer,
            config=sae_config,
            dataset_manager=dataset_manager,
        )

        # Collect some activations
        logger.info("Collecting activations...")
        with activations_store:
            activations = activations_store.collect_activations(
                num_batches=5,
                batch_size=8,
            )

        logger.info(f"✓ Collected activations: {activations.shape}")

        # 5. Analyze activations
        logger.info("\nStep 5: Analyzing activations")

        from spare.analysis.activation_analyzer import ActivationAnalyzer

        analyzer = ActivationAnalyzer()
        analysis = analyzer.analyze_activations(
            activations, compute_correlations=False  # Skip for speed
        )

        logger.info(f"✓ Activation analysis completed")
        logger.info(
            f"  Mean activation: {analysis['basic_stats'].mean.mean().item():.4f}"
        )
        logger.info(f"  Sparsity: {analysis['sparsity']['overall_sparsity']:.4f}")
        logger.info(
            f"  Active features per sample: {analysis['sparsity']['active_features_per_sample_mean']:.1f}"
        )

        # 6. Function extraction demo
        logger.info("\nStep 6: Function extraction demonstration")

        # Create our own SAE for function extraction (since we need training capabilities)
        from spare.core.sae import SAE

        demo_sae = SAE(
            d_in=model_info["hidden_size"],
            d_sae=model_info["hidden_size"] * 8,  # Smaller for demo
            architecture="standard",
            activation_fn="relu",
            normalize_decoder=True,
            device=activations.device,
        )

        # Test SAE on collected activations
        with torch.no_grad():
            sae_output = demo_sae(activations[:10])  # Use first 10 samples

        logger.info(f"✓ SAE test completed")
        logger.info(f"  Reconstruction sparsity: {sae_output.sparsity.item():.4f}")
        logger.info(f"  Reconstruction FVU: {sae_output.fvu.item():.4f}")

        # Function extraction
        function_extractor = FunctionExtractor(
            sae=demo_sae,
            device=activations.device,
        )

        # Create synthetic target for demo
        context_acts = activations[:5]
        target_acts = activations[5:10]

        result = function_extractor.extract_function(
            target_activations=target_acts,
            context_activations=context_acts,
            learning_rate=1e-3,
            num_iterations=100,
            verbose=False,
        )

        logger.info(f"✓ Function extraction completed")
        logger.info(f"  Active features: {len(result.active_features)}")
        logger.info(f"  Extraction strength: {result.extraction_strength:.6f}")

        logger.info("\n" + "=" * 60)
        logger.info("Llama 3.1 8B + EleutherAI SAE Demo Completed Successfully!")
        logger.info("=" * 60)

        return {
            "model": model,
            "tokenizer": tokenizer,
            "model_info": model_info,
            "activations": activations,
            "analysis": analysis,
            "sae_output": sae_output,
            "function_result": result,
        }

    except Exception as e:
        logger.error(f"✗ Llama 3.1 demo failed: {e}")
        import traceback

        traceback.print_exc()
        return None


def simple_usage_example():
    """
    Show a simple usage example of the new API.
    """
    logger.info("Simple Usage Example:")
    logger.info("-" * 30)

    # Create configuration
    config = spare.SAEConfig(
        model=spare.ModelConfig(model_name="microsoft/DialoGPT-medium"),
        dataset=spare.DatasetConfig(dataset_name="wikitext"),
        training=spare.TrainingConfig(total_training_tokens=100_000),
        hook_layer=6,
        expansion_factor=32,
    )

    # Train SAE (commented out for demo)
    # trainer = spare.SAETrainer(config)
    # sae = trainer.train()

    logger.info("✓ Configuration created successfully")
    logger.info("✓ Ready for training with spare.SAETrainer(config).train()")

    return config


if __name__ == "__main__":
    """
    Main demo script showcasing the new generalized SpARE framework.

    This demonstrates how the refactored library now works with any HuggingFace
    model and dataset, providing a much more flexible and powerful API.
    """

    import sys

    # Check if user wants to run specific demo
    if len(sys.argv) > 1 and sys.argv[1] == "--llama31":
        # Run Llama 3.1 8B Instruct + EleutherAI SAE demo
        logger.info("Running Llama 3.1 8B Instruct + EleutherAI SAE Demo")
        result = demonstrate_llama31_with_eleuther_sae()

        if result:
            logger.info("\n" + "=" * 60)
            logger.info("Demo completed successfully!")
            logger.info("=" * 60)
        else:
            logger.error("Demo failed - check error messages above")

    else:
        # Run the comprehensive demo
        run_comprehensive_demo()

        # Show simple usage example
        logger.info("\n" + "=" * 40)
        logger.info("Simple Usage Example")
        logger.info("=" * 40)
        simple_usage_example()

        logger.info("\n" + "=" * 60)
        logger.info("Key Improvements in the Refactored SpARE Library:")
        logger.info("=" * 60)
        logger.info("1. ✓ Universal HuggingFace model support (any architecture)")
        logger.info("2. ✓ Flexible dataset handling (any HuggingFace dataset)")
        logger.info("3. ✓ Multiple SAE architectures (standard, gated, jumprelu)")
        logger.info("4. ✓ Improved configuration management")
        logger.info("5. ✓ Better error handling and validation")
        logger.info("6. ✓ Comprehensive logging and monitoring")
        logger.info("7. ✓ Enhanced function extraction capabilities")
        logger.info("8. ✓ Software engineering best practices")
        logger.info("9. ✓ Modular and extensible design")
        logger.info("10. ✓ Backward compatibility with legacy functions")

        logger.info("\n" + "Usage Examples:")
        logger.info("-" * 20)
        logger.info("# Basic usage:")
        logger.info("import spare")
        logger.info("config = spare.SAEConfig(model_name='any-hf-model')")
        logger.info("trainer = spare.SAETrainer(config)")
        logger.info("sae = trainer.train()")
        logger.info("")
        logger.info("# Function extraction:")
        logger.info("extractor = spare.FunctionExtractor(sae)")
        logger.info("result = extractor.extract_function(target, context)")
        logger.info("")
        logger.info("# Model loading:")
        logger.info("model, tokenizer = spare.load_model_and_tokenizer('any-hf-model')")

        logger.info("\n" + "To run Llama 3.1 8B demo:")
        logger.info("python demo.py --llama31")

    logger.info("\n" + "=" * 60)
    logger.info("Thank you for using SpARE!")
    logger.info("=" * 60)
